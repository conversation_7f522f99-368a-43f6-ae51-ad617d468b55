# Required NPM Packages for Next.js E-commerce Migration

## Core Dependencies

### State Management & Data Fetching
```bash
npm install zustand@^5.0.6
npm install @tanstack/react-query@^5.35.1
npm install @tanstack/react-query-persist-client@^5.79.0
npm install @tanstack/query-sync-storage-persister@^5.79.0
```

### HTTP Client & API
```bash
npm install axios@^1.6.8
```

### Forms & Validation
```bash
npm install react-hook-form@^7.51.5
npm install @hookform/resolvers@^3.4.2
npm install zod@^3.23.8
```

### Styling
```bash
npm install sass@^1.76.0
```

### UI & Icons
```bash
npm install react-icons@^5.2.0
npm install react-spinners@^0.13.8
```

### Date & Utilities
```bash
npm install date-fns@^4.1.0
npm install lodash@^4.17.21
npm install dompurify@^3.1.7
```

### Payment Integration
```bash
npm install @stripe/react-stripe-js@^2.7.3
npm install @stripe/stripe-js@^4.0.0
npm install @paypal/react-paypal-js@^8.3.0
npm install braintree-web@^3.113.0
```

### Phone Input
```bash
npm install react-phone-input-2@^2.15.1
```

## Development Dependencies

### TypeScript Types
```bash
npm install --save-dev @types/lodash@^4.17.0
npm install --save-dev @types/dompurify@^3.0.5
npm install --save-dev @types/braintree-web@^3.96.16
```

### Development Tools
```bash
npm install --save-dev @tanstack/react-query-devtools@^5.35.1
npm install --save-dev @hookform/devtools@^4.3.1
```

### Testing (Optional - for future implementation)
```bash
npm install --save-dev vitest@^2.0.0
npm install --save-dev @testing-library/react@^16.0.0
npm install --save-dev @testing-library/jest-dom@^6.0.0
npm install --save-dev @playwright/test@^1.40.0
npm install --save-dev msw@^2.0.0
```

### Code Quality (Optional - enhanced linting)
```bash
npm install --save-dev prettier@^3.0.0
npm install --save-dev eslint-config-prettier@^9.0.0
npm install --save-dev eslint-plugin-prettier@^5.0.0
```

## Installation Commands

### Essential Packages (Run First)
```bash
# Core functionality packages
npm install zustand@^5.0.6 @tanstack/react-query@^5.35.1 @tanstack/react-query-persist-client@^5.79.0 @tanstack/query-sync-storage-persister@^5.79.0 axios@^1.6.8

# Forms and validation
npm install react-hook-form@^7.51.5 @hookform/resolvers@^3.4.2 zod@^3.23.8

# Styling and UI
npm install sass@^1.76.0 react-icons@^5.2.0 react-spinners@^0.13.8

# Utilities
npm install date-fns@^4.1.0 lodash@^4.17.21 dompurify@^3.1.7 react-phone-input-2@^2.15.1
```

### Payment Integration (Run Second)
```bash
npm install @stripe/react-stripe-js@^2.7.3 @stripe/stripe-js@^4.0.0 @paypal/react-paypal-js@^8.3.0 braintree-web@^3.113.0
```

### Development Dependencies (Run Third)
```bash
npm install --save-dev @types/lodash@^4.17.0 @types/dompurify@^3.0.5 @types/braintree-web@^3.96.16 @tanstack/react-query-devtools@^5.35.1 @hookform/devtools@^4.3.1
```

## Package Justification

### Core Architecture
- **Zustand**: Lightweight state management inspired by admin-arena patterns
- **TanStack Query**: Server state management with caching and persistence
- **Axios**: HTTP client with interceptors for API communication

### Developer Experience
- **React Hook Form + Zod**: Type-safe form handling with validation
- **SCSS**: Enhanced styling capabilities with variables and mixins
- **React Icons**: Comprehensive icon library
- **TypeScript Types**: Enhanced development experience with proper typing

### E-commerce Specific
- **Payment Libraries**: Support for Stripe, PayPal, and Braintree
- **Date-fns**: Modern date manipulation library
- **DOMPurify**: XSS protection for user-generated content
- **React Phone Input**: International phone number input

### Performance & UX
- **React Spinners**: Loading indicators for better UX
- **Query Persistence**: Offline support and faster loading
- **Lodash**: Utility functions for data manipulation

## Version Compatibility

All packages are selected for compatibility with:
- **Next.js 15.4.6**
- **React 19.1.0**
- **TypeScript 5.x**
- **Node.js 18+**

## Notes

1. **Payment Integration**: Payment packages can be installed selectively based on business requirements
2. **Testing**: Testing packages are optional for initial implementation but recommended for production
3. **Code Quality**: Prettier and enhanced ESLint configs are optional but recommended
4. **Development Tools**: Query and form devtools are helpful during development but not required for production

## Post-Installation Steps

After installing packages, you'll need to:

1. Configure environment variables for API endpoints and payment keys
2. Setup SCSS global imports in Next.js config
3. Configure TanStack Query client with persistence
4. Setup Zustand stores with proper TypeScript typing
5. Configure Axios interceptors for authentication and error handling

## Estimated Bundle Impact

- **Core packages**: ~400KB gzipped
- **Payment libraries**: ~200KB gzipped (loaded dynamically)
- **Development tools**: 0KB (dev-only)
- **Total production bundle**: ~600KB gzipped

This is within acceptable limits for a modern e-commerce application and includes significant functionality for state management, data fetching, forms, and payment processing.
